import Foundation
import SwiftData

public protocol NFCRecordServiceProtocol {
  func getAll() throws -> [Content]
  func save(_ record: Content) async throws
  func deleteAll() throws
}

public struct NFCRecordService: NFCRecordServiceProtocol {
  private let context: ModelContext

  public init(context: ModelContext) {
    self.context = context
  }

  // Temporary code until we find a better solution
  public func getAll() throws -> [Content] {
    var fetchDescriptor = FetchDescriptor<Content>()
    fetchDescriptor.sortBy = [SortDescriptor(\Content.createdAt, order: .reverse)]
    fetchDescriptor.fetchLimit = 20

    let existingRecords = try context.fetch(fetchDescriptor)

    let uniqueRecords = existingRecords.uniqued(on: { record in
      record.url ?? ""
    })
    return Array(uniqueRecords)
  }

  public func save(_ record: Content) async throws {
    await Task.detached {
      context.insert(record)
      try context.save()
    }.value
  }

  public func deleteAll() throws {
    try context.delete(model: Content.self)
    try context.save()
  }
}
