import CrateServices
import Factory
import SwiftData
import SwiftUI

public enum UIFlags: Int {
  case showAboutView
  case showCardPreview
  case showEmptyUrlWriteAlert
  case showExtraDetailsView
  case isWritingToNFC
}

public enum WriteViewState {
  case empty
  case fetching
  case loaded(Content)
  case error(String)
}

public struct ViewState {
  var enteredURL: String = ""
  var currentURL: String = ""
  var imageURL: URL?
  var domainImage: String?
  var lastWrittenRecord: Content?
}

@MainActor
public final class WriteViewModel: ObservableObject {
  @Published public var viewState = ViewState()
  @Published public var state: WriteViewState = .empty
  @Published public var activeFlags: Set<UIFlags> = []
  @Published public var nfcWriter = NFCWriter()
  @Published public var showExtraDetails: Bool = false
  @Published public var customCreator: String = "lilrobo"
  private var debounceWorkItem: DispatchWorkItem?

  private let unfurlService: UnfurlServiceProtocol
  private let nfcRecordService: NFCRecordServiceProtocol
  private let contentService: ContentServiceProtocol
  public var deepLinkHandler: DeepLinkHandler
  private var crateActor: CrateActor

  public init(_ deepLinkHandler: DeepLinkHandler) {
    self.deepLinkHandler = deepLinkHandler
    self.crateActor = Container.shared.crateActor.resolve()
    unfurlService = Container.shared.unfurlService.resolve()
    nfcRecordService = Container.shared.nfcRecordService.resolve()
    contentService = Container.shared.contentService.resolve()
  }

  public func fetchContentInfo() async {
    do {
      state = .fetching
      guard let url = URL(string: viewState.currentURL) else {
        state = .error("Invalid URL")
        return
      }

      // Use client-side unfurl ONLY.
      let contentData = try await unfurlService.unfurl(url: url).toModel()
      viewState.imageURL = URL(string: contentData.mediaUrl ?? "")
      state = .loaded(contentData)

    } catch {
      print("Error: \(error)")
      state = .error(error.localizedDescription)
    }
  }

  private func handleSignedInWrite(_ record: Content) async {
    // Write NFC message immediately - this is what the user expects to happen right away
    writeNFCMessage(record)

    // Then handle server operations in the background
    do {
      // Unfurl the URL to create content on the server
      let serverContentDTO = try await contentService.unfurl(url: record.url ?? "")

      // Add to recent collection using CrateActor
      try await addToRecentCollection(serverContentDTO)

      print(
        "Successfully unfurled and created content on server with ID: \(serverContentDTO.serverId ?? -1) and added to recent content"
      )
    } catch {
      print("Error unfurling content on server or syncing: \(error.localizedDescription)")
      // NFC write already succeeded, so this is just a background sync failure
    }
  }

  private func handleGuestWrite(_ record: Content) async throws {
    // Convert Content to ContentDTO for CrateActor
    let contentDTO = record.toDTO()

    // Add to recent collection using CrateActor
    try await addToRecentCollection(contentDTO)

    writeNFCMessage(record)
  }

  public func handleWriteButtonTapped() {
    let record = createRecordFromCurrentState()
    viewState.lastWrittenRecord = record

    // Set writing state to provide user feedback
    activeFlags.insert(.isWritingToNFC)

    // Move all operations to background to avoid blocking UI
    Task {
      defer {
        // Always clear the writing state when done
        Task { @MainActor in
          activeFlags.remove(.isWritingToNFC)
        }
      }

      do {
        try await nfcRecordService.save(record)

        // Get the user's authentication state
        let isSignedIn = Container.shared.userState.resolve().isSignedIn

        if isSignedIn {
          await handleSignedInWrite(record)
        } else {
          try await handleGuestWrite(record)
        }
      } catch {
        print("Error saving record or updating collection: \(error.localizedDescription)")
      }
    }
  }

  private func addToRecentCollection(_ contentDTO: ContentDTO) async throws {
    // Ensure Recent collection exists
    let recentCollection = try await crateActor.fetchRecentCollection()
    if recentCollection == nil {
      try await crateActor.addRecentCollection(
        name: "Recent",
        thumbnail: "crate_logo",
        content: []
      )
    }

    // Get current content and add the new item
    let currentContent = try await crateActor.fetchRecentCollection()?.contents ?? []
    let updatedContent = [contentDTO] + currentContent

    // Update the Recent collection with the new content
    try await crateActor.updateRecentCollection(content: updatedContent)
  }

  private func createRecordFromCurrentState() -> Content {
    switch state {
    case let .loaded(existingRecord):
      return Content(
        detail: existingRecord.detail,
        title: existingRecord.title,
        mediaUrl: viewState.imageURL?.absoluteString,
        url: viewState.enteredURL,
        updatedAt: Date(),
        createdAt: Date()
      )
    case let .error(errorMessage):
      print("Creating basic record due to unfurl error: \(errorMessage)")
      return Content(
        detail: nil,
        title: nil,
        mediaUrl: nil,
        url: viewState.enteredURL,
        updatedAt: Date(),
        createdAt: Date()
      )
    case .empty, .fetching:
      return Content(
        detail: nil,
        title: nil,
        mediaUrl: nil,
        url: viewState.enteredURL,
        updatedAt: Date(),
        createdAt: Date()
      )
    }
  }

  public func handleURLChange() {
    debounceWorkItem?.cancel()

    let workItem = DispatchWorkItem { [weak self] in
      guard let self = self else { return }

      if self.viewState.currentURL.isEmpty {
        self.state = .empty
        return
      }

      Task {
        await self.fetchContentInfo()
      }
    }

    debounceWorkItem = workItem
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: workItem)  // 300ms debounce
  }

  public func clearContentInfo() {
    viewState.enteredURL = ""
    viewState.currentURL = ""
    viewState.imageURL = nil
    viewState.domainImage = nil
    state = .empty
  }

  public func handleDeepLink(_ url: URL?) {
    if let deepLink = url?.absoluteString {
      viewState.enteredURL = deepLink
      viewState.currentURL = deepLink
      handleURLChange()
    }
  }

  public func handleInitialDeepLink() {
    if let deepLink = deepLinkHandler.deepLinkURL,
       !deepLink.absoluteString.isEmpty {
      viewState.enteredURL = deepLink.absoluteString
      viewState.currentURL = deepLink.absoluteString
      handleURLChange()
    }
  }

  public func handleRefresh() {
    if let deepLink = deepLinkHandler.deepLinkURL?.absoluteString {
      print("Current deep link on refresh: \(deepLink)")
    }
    handleURLChange()
  }

  public func writeNFCMessage(_ record: Content) {
    let messages: [(String, String)] = [
      (record.url ?? "", "U"),
      (createMetadataJSONFromRecord(record), "T")
    ]
    print("Starting NFC write with messages: \(messages)")
    nfcWriter.write(messages)
  }

  public func createMetadataJSONFromRecord(_ record: Content) -> String {
    var metadata: [String: String] = [:]
    metadata["created"] = ISO8601DateFormatter().string(from: Date())

    if showExtraDetails {
      if let detail = record.detail, !detail.isEmpty {
        metadata["artist"] = detail
      }
      if let title = record.title, !title.isEmpty {
        metadata["song"] = title
      }
      if !customCreator.isEmpty {
        metadata["creator"] = customCreator
      }
      if let mediaUrl = record.mediaUrl, !mediaUrl.isEmpty {
        metadata["imgUrl"] = mediaUrl
      }
      if let url = record.url, let domain = URL(string: url)?.host, !domain.isEmpty {
        metadata["domain"] = domain
      }
    }

    guard let jsonData = try? JSONSerialization.data(withJSONObject: metadata),
          let jsonString = String(data: jsonData, encoding: .utf8)
    else {
      print("Error creating metadata JSON")
      return "{}"
    }

    print("Writing metadata: \(jsonString)")
    return jsonString
  }
}
